import 'package:freezed_annotation/freezed_annotation.dart';
import '../errors/failures.dart';

part 'result.freezed.dart';

/// Result type for handling success and failure states
@freezed
class Result<T> with _$Result<T> {
  const factory Result.success(T data) = Success<T>;
  const factory Result.failure(Failure failure) = Failed<T>;
}

/// Extension methods for Result
extension ResultExtension<T> on Result<T> {
  /// Returns true if the result is a success
  bool get isSuccess => when(
        success: (_) => true,
        failure: (_) => false,
      );

  /// Returns true if the result is a failure
  bool get isFailure => !isSuccess;

  /// Gets the data if success, null otherwise
  T? get data => when(
        success: (data) => data,
        failure: (_) => null,
      );

  /// Gets the failure if failed, null otherwise
  Failure? get failure => when(
        success: (_) => null,
        failure: (failure) => failure,
      );

  /// Transforms the success value
  Result<R> map<R>(R Function(T) transform) {
    return when(
      success: (data) => Result.success(transform(data)),
      failure: (failure) => Result.failure(failure),
    );
  }

  /// Chains another result-returning operation
  Result<R> flatMap<R>(Result<R> Function(T) transform) {
    return when(
      success: (data) => transform(data),
      failure: (failure) => Result.failure(failure),
    );
  }
}
