import 'dart:io';
import 'package:drift/drift.dart';
import 'package:drift/native.dart';
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as p;
import '../config/app_config.dart';

// Import table definitions
import '../../features/income/data/datasources/income_table.dart';

part 'database.g.dart';

/// Main application database
@DriftDatabase(tables: [IncomeTable])
class AppDatabase extends _$AppDatabase {
  AppDatabase() : super(_openConnection());

  @override
  int get schemaVersion => AppConfig.databaseVersion;

  @override
  MigrationStrategy get migration {
    return MigrationStrategy(
      onCreate: (Migrator m) async {
        await m.createAll();
      },
      onUpgrade: (Migrator m, int from, int to) async {
        // Handle database migrations here
        if (from < 2) {
          // Example migration for version 2
          // await m.addColumn(incomeTable, incomeTable.newColumn);
        }
      },
    );
  }
}

/// Opens database connection
LazyDatabase _openConnection() {
  return LazyDatabase(() async {
    final dbFolder = await getApplicationDocumentsDirectory();
    final file = File(p.join(dbFolder.path, AppConfig.databaseName));
    
    return NativeDatabase.createInBackground(
      file,
      logStatements: AppConfig.enableDatabaseLogging,
    );
  });
}
