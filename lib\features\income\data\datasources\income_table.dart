import 'package:drift/drift.dart';

/// Income table definition for Drift ORM
@DataClassName('IncomeData')
class IncomeTable extends Table {
  /// Primary key UUID
  TextColumn get uuid => text().withLength(min: 36, max: 36)();

  /// Auto-increment ID
  IntColumn get id => integer()();

  /// Date of income record
  DateTimeColumn get date => dateTime()();

  /// Initial mileage
  IntColumn get initialMileage => integer()();

  /// Final mileage
  IntColumn get finalMileage => integer()();

  /// Initial GoPay amount
  RealColumn get initialGopay => real()();

  /// Initial BCA amount
  RealColumn get initialBca => real()();

  /// Initial Cash amount
  RealColumn get initialCash => real()();

  /// Initial OVO amount
  RealColumn get initialOvo => real()();

  /// Initial BRI amount
  RealColumn get initialBri => real()();

  /// Initial Rekpon amount
  RealColumn get initialRekpon => real()();

  /// Final GoPay amount
  RealColumn get finalGopay => real()();

  /// Final BCA amount
  RealColumn get finalBca => real()();

  /// Final Cash amount
  RealColumn get finalCash => real()();

  /// Final OVO amount
  RealColumn get finalOvo => real()();

  /// Final BRI amount
  RealColumn get finalBri => real()();

  /// Final Rekpon amount
  RealColumn get finalRekpon => real()();

  /// Record creation timestamp
  DateTimeColumn get createdAt => dateTime().withDefault(currentDateAndTime)();

  /// Record update timestamp
  DateTimeColumn get updatedAt => dateTime().withDefault(currentDateAndTime)();

  /// Soft delete timestamp
  DateTimeColumn get deletedAt => dateTime().nullable()();

  /// Sync status for cloud synchronization
  TextColumn get syncStatus =>
      text().withDefault(const Constant('pendingUpload'))();

  @override
  Set<Column> get primaryKey => {uuid};

  @override
  List<String> get customConstraints => [
    'CHECK (final_mileage >= initial_mileage)',
    'CHECK (initial_mileage >= 0)',
    'CHECK (initial_gopay >= 0)',
    'CHECK (initial_bca >= 0)',
    'CHECK (initial_cash >= 0)',
    'CHECK (initial_ovo >= 0)',
    'CHECK (initial_bri >= 0)',
    'CHECK (initial_rekpon >= 0)',
    'CHECK (final_gopay >= 0)',
    'CHECK (final_bca >= 0)',
    'CHECK (final_cash >= 0)',
    'CHECK (final_ovo >= 0)',
    'CHECK (final_bri >= 0)',
    'CHECK (final_rekpon >= 0)',
  ];
}
