import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:intl/intl.dart';
import '../../domain/entities/income.dart';
import '../../data/providers/income_repository_provider.dart';
import '../providers/income_providers.dart';
import '../widgets/income_card.dart';
import 'income_form_screen.dart';

class IncomeListScreen extends ConsumerWidget {
  const IncomeListScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final incomeListAsync = ref.watch(incomeListProvider);
    final currentMonthTotalAsync = ref.watch(currentMonthTotalProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Income Records'),
        actions: [
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: () async {
              await Navigator.of(context).push(
                MaterialPageRoute(
                  builder: (context) => const IncomeFormScreen(),
                ),
              );
              // Refresh the list when returning from form
              ref.invalidate(incomeListProvider);
              ref.invalidate(currentMonthTotalProvider);
            },
          ),
        ],
      ),
      body: Column(
        children: [
          // Summary Card
          Card(
            margin: const EdgeInsets.all(16),
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'This Month Summary',
                    style: Theme.of(context).textTheme.titleMedium,
                  ),
                  const SizedBox(height: 8),
                  currentMonthTotalAsync.when(
                    data: (total) => Text(
                      'Total Income: ${NumberFormat.currency(locale: 'id_ID', symbol: 'Rp ', decimalDigits: 0).format(total)}',
                      style: Theme.of(context).textTheme.headlineSmall
                          ?.copyWith(
                            color: total >= 0 ? Colors.green : Colors.red,
                            fontWeight: FontWeight.bold,
                          ),
                    ),
                    loading: () => const CircularProgressIndicator(),
                    error: (error, stack) => Text(
                      'Error loading total: $error',
                      style: TextStyle(
                        color: Theme.of(context).colorScheme.error,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),

          // Income List
          Expanded(
            child: incomeListAsync.when(
              data: (incomes) {
                if (incomes.isEmpty) {
                  return const Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(Icons.receipt_long, size: 64, color: Colors.grey),
                        SizedBox(height: 16),
                        Text(
                          'No income records yet',
                          style: TextStyle(fontSize: 18, color: Colors.grey),
                        ),
                        SizedBox(height: 8),
                        Text(
                          'Tap the + button to add your first income record',
                          style: TextStyle(color: Colors.grey),
                        ),
                      ],
                    ),
                  );
                }

                return ListView.builder(
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  itemCount: incomes.length,
                  itemBuilder: (context, index) {
                    final income = incomes[index];
                    return IncomeCard(
                      income: income,
                      onTap: () => _editIncome(context, income),
                      onDelete: () => _deleteIncome(context, ref, income),
                    );
                  },
                );
              },
              loading: () => const Center(child: CircularProgressIndicator()),
              error: (error, stack) => Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.error_outline,
                      size: 64,
                      color: Theme.of(context).colorScheme.error,
                    ),
                    const SizedBox(height: 16),
                    Text(
                      'Error loading income records',
                      style: TextStyle(
                        fontSize: 18,
                        color: Theme.of(context).colorScheme.error,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      error.toString(),
                      style: const TextStyle(color: Colors.grey),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 16),
                    ElevatedButton(
                      onPressed: () => ref.refresh(incomeListProvider),
                      child: const Text('Retry'),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _editIncome(BuildContext context, Income income) async {
    await Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) =>
            IncomeFormScreen(initialIncome: income, isEditing: true),
      ),
    );
    // Refresh the list when returning from form
    if (context.mounted) {
      final ref = ProviderScope.containerOf(context);
      ref.invalidate(incomeListProvider);
      ref.invalidate(currentMonthTotalProvider);
    }
  }

  void _deleteIncome(BuildContext context, WidgetRef ref, Income income) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Income Record'),
        content: Text(
          'Are you sure you want to delete the income record for ${DateFormat('MMM dd, yyyy').format(income.date)}?',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () async {
              Navigator.of(context).pop();

              final repository = ref.read(incomeRepositoryProvider);
              final result = await repository.delete(income.id!);

              result.when(
                success: (_) {
                  ref.invalidate(incomeListProvider);
                  ref.invalidate(currentMonthTotalProvider);

                  if (context.mounted) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text('Income record deleted'),
                        backgroundColor: Colors.green,
                      ),
                    );
                  }
                },
                failure: (failure) {
                  if (context.mounted) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text(
                          'Error deleting record: ${failure.message}',
                        ),
                        backgroundColor: Colors.red,
                      ),
                    );
                  }
                },
              );
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }
}
