import 'package:riverpod_annotation/riverpod_annotation.dart';
import '../../../../core/providers/database_provider.dart';
import '../../domain/repositories/income_repository.dart';
import '../repositories/income_repository_impl.dart';

part 'income_repository_provider.g.dart';

/// Provides the income repository instance
@Riverpod(keepAlive: true)
IncomeRepository incomeRepository(IncomeRepositoryRef ref) {
  final database = ref.watch(databaseProvider);
  return IncomeRepositoryImpl(database);
}
