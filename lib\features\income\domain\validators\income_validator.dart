import '../entities/income.dart';

/// Validation utilities for income data
class IncomeValidator {
  /// Validates an income entity
  static List<String> validate(Income income) {
    final errors = <String>[];

    // Validate mileage
    if (income.finalMileage < income.initialMileage) {
      errors.add('Final mileage cannot be less than initial mileage');
    }

    if (income.initialMileage < 0) {
      errors.add('Initial mileage cannot be negative');
    }

    if (income.finalMileage < 0) {
      errors.add('Final mileage cannot be negative');
    }

    // Validate payment amounts (now allow negative values for refunds/corrections)
    final paymentFields = [
      ('Initial GoPay', income.initialGopay),
      ('Initial BCA', income.initialBca),
      ('Initial Cash', income.initialCash),
      ('Initial OVO', income.initialOvo),
      ('Initial BRI', income.initialBri),
      ('Initial Rekpon', income.initialRekpon),
      ('Final GoPay', income.finalGopay),
      ('Final BCA', income.finalBca),
      ('Final Cash', income.finalCash),
      ('Final OVO', income.finalOvo),
      ('Final BRI', income.finalBri),
      ('Final Rekpon', income.finalRekpon),
    ];

    // Check for unreasonable values (both positive and negative)
    for (final (fieldName, value) in paymentFields) {
      if (value > 999999999 || value < -999999999) {
        errors.add('$fieldName amount seems unreasonable');
      }
    }

    // Validate date
    final now = DateTime.now();
    if (income.date.isAfter(now)) {
      errors.add('Income date cannot be in the future');
    }

    // Check for reasonable date range (not more than 10 years ago)
    final tenYearsAgo = now.subtract(const Duration(days: 365 * 10));
    if (income.date.isBefore(tenYearsAgo)) {
      errors.add('Income date cannot be more than 10 years ago');
    }

    return errors;
  }

  /// Validates if the income data is complete
  static bool isComplete(Income income) {
    return income.initialMileage > 0 &&
        income.finalMileage > 0 &&
        (income.initialCapital > 0 || income.finalResult > 0);
  }

  /// Validates mileage input
  static String? validateMileage(String? value, {bool isInitial = true}) {
    if (value == null || value.isEmpty) {
      return '${isInitial ? 'Initial' : 'Final'} mileage is required';
    }

    final mileage = int.tryParse(value);
    if (mileage == null) {
      return 'Please enter a valid number';
    }

    if (mileage < 0) {
      return 'Mileage cannot be negative';
    }

    if (mileage > 9999999) {
      return 'Mileage seems too high';
    }

    return null;
  }

  /// Validates payment amount input - now optional and allows negative values
  static String? validateAmount(String? value, String fieldName) {
    // Payment fields are now optional - empty values are allowed
    if (value == null || value.isEmpty) {
      return null;
    }

    final amount = double.tryParse(value);
    if (amount == null) {
      return 'Please enter a valid amount';
    }

    // Allow negative values for refunds/corrections
    if (amount > 999999999 || amount < -999999999) {
      return 'Amount seems too high';
    }

    return null;
  }

  /// Validates date input
  static String? validateDate(DateTime? date) {
    if (date == null) {
      return 'Date is required';
    }

    final now = DateTime.now();
    if (date.isAfter(now)) {
      return 'Date cannot be in the future';
    }

    final tenYearsAgo = now.subtract(const Duration(days: 365 * 10));
    if (date.isBefore(tenYearsAgo)) {
      return 'Date cannot be more than 10 years ago';
    }

    return null;
  }
}
