import 'package:drift/drift.dart';
import 'package:uuid/uuid.dart';
import '../../domain/entities/income.dart';
import '../../../../core/datasources/database.dart';

/// Extension to convert between Income entity and IncomeData (Drift model)
extension IncomeModelExtension on Income {
  /// Convert Income entity to IncomeTableCompanion for database operations
  IncomeTableCompanion toCompanion() {
    return IncomeTableCompanion(
      uuid: Value(uuid ?? const Uuid().v4()),
      id: id != null ? Value(id!) : const Value.absent(),
      date: Value(date),
      initialMileage: Value(initialMileage),
      finalMileage: Value(finalMileage),
      initialGopay: Value(initialGopay),
      initialBca: Value(initialBca),
      initialCash: Value(initialCash),
      initialOvo: Value(initialOvo),
      initialBri: Value(initialBri),
      initialRekpon: Value(initialRekpon),
      finalGopay: Value(finalGopay),
      finalBca: Value(finalBca),
      finalCash: Value(finalCash),
      finalOvo: Value(finalOvo),
      finalBri: Value(finalBri),
      finalRekpon: Value(finalRekpon),
      createdAt: createdAt != null ? Value(createdAt!) : const Value.absent(),
      updatedAt: Value(updatedAt ?? DateTime.now()),
      deletedAt: deletedAt != null ? Value(deletedAt!) : const Value.absent(),
      syncStatus: Value(syncStatus),
    );
  }

  /// Convert Income entity to IncomeTableCompanion for updates
  IncomeTableCompanion toUpdateCompanion() {
    return IncomeTableCompanion(
      uuid: Value(uuid!),
      id: id != null ? Value(id!) : const Value.absent(),
      date: Value(date),
      initialMileage: Value(initialMileage),
      finalMileage: Value(finalMileage),
      initialGopay: Value(initialGopay),
      initialBca: Value(initialBca),
      initialCash: Value(initialCash),
      initialOvo: Value(initialOvo),
      initialBri: Value(initialBri),
      initialRekpon: Value(initialRekpon),
      finalGopay: Value(finalGopay),
      finalBca: Value(finalBca),
      finalCash: Value(finalCash),
      finalOvo: Value(finalOvo),
      finalBri: Value(finalBri),
      finalRekpon: Value(finalRekpon),
      updatedAt: Value(DateTime.now()),
      deletedAt: deletedAt != null ? Value(deletedAt!) : const Value.absent(),
      syncStatus: Value(syncStatus),
    );
  }
}

/// Extension to convert IncomeData to Income entity
extension IncomeDataExtension on IncomeData {
  /// Convert IncomeData (Drift model) to Income entity
  Income toEntity() {
    return Income(
      uuid: uuid,
      id: id,
      date: date,
      initialMileage: initialMileage,
      finalMileage: finalMileage,
      initialGopay: initialGopay,
      initialBca: initialBca,
      initialCash: initialCash,
      initialOvo: initialOvo,
      initialBri: initialBri,
      initialRekpon: initialRekpon,
      finalGopay: finalGopay,
      finalBca: finalBca,
      finalCash: finalCash,
      finalOvo: finalOvo,
      finalBri: finalBri,
      finalRekpon: finalRekpon,
      createdAt: createdAt,
      updatedAt: updatedAt,
      deletedAt: deletedAt,
      syncStatus: syncStatus,
    );
  }
}
