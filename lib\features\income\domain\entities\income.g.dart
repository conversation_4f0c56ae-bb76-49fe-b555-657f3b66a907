// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'income.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$IncomeImpl _$$IncomeImplFromJson(Map<String, dynamic> json) => _$IncomeImpl(
  uuid: json['uuid'] as String?,
  id: (json['id'] as num?)?.toInt(),
  date: DateTime.parse(json['date'] as String),
  initialMileage: (json['initialMileage'] as num).toInt(),
  finalMileage: (json['finalMileage'] as num).toInt(),
  initialGopay: (json['initialGopay'] as num).toDouble(),
  initialBca: (json['initialBca'] as num).toDouble(),
  initialCash: (json['initialCash'] as num).toDouble(),
  initialOvo: (json['initialOvo'] as num).toDouble(),
  initialBri: (json['initialBri'] as num).toDouble(),
  initialRekpon: (json['initialRekpon'] as num).toDouble(),
  finalGopay: (json['finalGopay'] as num).toDouble(),
  finalBca: (json['finalBca'] as num).toDouble(),
  finalCash: (json['finalCash'] as num).toDouble(),
  finalOvo: (json['finalOvo'] as num).toDouble(),
  finalBri: (json['finalBri'] as num).toDouble(),
  finalRekpon: (json['finalRekpon'] as num).toDouble(),
  createdAt: json['createdAt'] == null
      ? null
      : DateTime.parse(json['createdAt'] as String),
  updatedAt: json['updatedAt'] == null
      ? null
      : DateTime.parse(json['updatedAt'] as String),
  deletedAt: json['deletedAt'] == null
      ? null
      : DateTime.parse(json['deletedAt'] as String),
  syncStatus: json['syncStatus'] as String? ?? 'pendingUpload',
);

Map<String, dynamic> _$$IncomeImplToJson(_$IncomeImpl instance) =>
    <String, dynamic>{
      if (instance.uuid case final value?) 'uuid': value,
      if (instance.id case final value?) 'id': value,
      'date': instance.date.toIso8601String(),
      'initialMileage': instance.initialMileage,
      'finalMileage': instance.finalMileage,
      'initialGopay': instance.initialGopay,
      'initialBca': instance.initialBca,
      'initialCash': instance.initialCash,
      'initialOvo': instance.initialOvo,
      'initialBri': instance.initialBri,
      'initialRekpon': instance.initialRekpon,
      'finalGopay': instance.finalGopay,
      'finalBca': instance.finalBca,
      'finalCash': instance.finalCash,
      'finalOvo': instance.finalOvo,
      'finalBri': instance.finalBri,
      'finalRekpon': instance.finalRekpon,
      if (instance.createdAt?.toIso8601String() case final value?)
        'createdAt': value,
      if (instance.updatedAt?.toIso8601String() case final value?)
        'updatedAt': value,
      if (instance.deletedAt?.toIso8601String() case final value?)
        'deletedAt': value,
      'syncStatus': instance.syncStatus,
    };
