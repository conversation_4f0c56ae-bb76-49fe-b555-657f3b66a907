import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:uuid/uuid.dart';
import '../../data/providers/income_repository_provider.dart';
import '../../domain/entities/income.dart';
import '../../domain/validators/income_validator.dart';
import '../providers/income_providers.dart';

class IncomeFormScreen extends HookConsumerWidget {
  final Income? initialIncome;
  final bool isEditing;

  const IncomeFormScreen({
    super.key,
    this.initialIncome,
    this.isEditing = false,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final formKey = useMemoized(() => GlobalKey<FormState>());

    // Date controller
    final dateController = useTextEditingController(
      text:
          initialIncome?.date.toString().split(' ')[0] ??
          DateTime.now().toString().split(' ')[0],
    );
    final selectedDate = useState(initialIncome?.date ?? DateTime.now());

    // Mileage controllers
    final initialMileageController = useTextEditingController(
      text: initialIncome?.initialMileage.toString() ?? '',
    );
    final finalMileageController = useTextEditingController(
      text: initialIncome?.finalMileage.toString() ?? '',
    );

    // Initial payment controllers - start empty for new entries, show values for editing
    final initialGopayController = useTextEditingController(
      text: initialIncome != null ? initialIncome!.initialGopay.toString() : '',
    );
    final initialBcaController = useTextEditingController(
      text: initialIncome != null ? initialIncome!.initialBca.toString() : '',
    );
    final initialCashController = useTextEditingController(
      text: initialIncome != null ? initialIncome!.initialCash.toString() : '',
    );
    final initialOvoController = useTextEditingController(
      text: initialIncome != null ? initialIncome!.initialOvo.toString() : '',
    );
    final initialBriController = useTextEditingController(
      text: initialIncome != null ? initialIncome!.initialBri.toString() : '',
    );
    final initialRekponController = useTextEditingController(
      text: initialIncome != null
          ? initialIncome!.initialRekpon.toString()
          : '',
    );

    // Final payment controllers - start empty for new entries, show values for editing
    final finalGopayController = useTextEditingController(
      text: initialIncome != null ? initialIncome!.finalGopay.toString() : '',
    );
    final finalBcaController = useTextEditingController(
      text: initialIncome != null ? initialIncome!.finalBca.toString() : '',
    );
    final finalCashController = useTextEditingController(
      text: initialIncome != null ? initialIncome!.finalCash.toString() : '',
    );
    final finalOvoController = useTextEditingController(
      text: initialIncome != null ? initialIncome!.finalOvo.toString() : '',
    );
    final finalBriController = useTextEditingController(
      text: initialIncome != null ? initialIncome!.finalBri.toString() : '',
    );
    final finalRekponController = useTextEditingController(
      text: initialIncome != null ? initialIncome!.finalRekpon.toString() : '',
    );

    final isLoading = useState(false);

    Future<void> selectDate() async {
      final picked = await showDatePicker(
        context: context,
        initialDate: selectedDate.value,
        firstDate: DateTime.now().subtract(const Duration(days: 365 * 10)),
        lastDate: DateTime.now(),
      );

      if (picked != null) {
        selectedDate.value = picked;
        dateController.text = picked.toString().split(' ')[0];
      }
    }

    Future<void> saveIncome() async {
      if (!formKey.currentState!.validate()) return;

      isLoading.value = true;

      try {
        // Helper function to parse double with default value
        double parseDoubleOrDefault(String text) {
          if (text.isEmpty) return 0.0;
          return double.tryParse(text) ?? 0.0;
        }

        // In add mode, set final mileage equal to initial mileage
        final initialMileage = int.parse(initialMileageController.text);
        final finalMileage = isEditing
            ? int.parse(finalMileageController.text)
            : initialMileage;

        final income = Income(
          uuid: initialIncome?.uuid ?? const Uuid().v4(),
          id: initialIncome?.id,
          date: selectedDate.value,
          initialMileage: initialMileage,
          finalMileage: finalMileage,
          initialGopay: parseDoubleOrDefault(initialGopayController.text),
          initialBca: parseDoubleOrDefault(initialBcaController.text),
          initialCash: parseDoubleOrDefault(initialCashController.text),
          initialOvo: parseDoubleOrDefault(initialOvoController.text),
          initialBri: parseDoubleOrDefault(initialBriController.text),
          initialRekpon: parseDoubleOrDefault(initialRekponController.text),
          finalGopay: parseDoubleOrDefault(finalGopayController.text),
          finalBca: parseDoubleOrDefault(finalBcaController.text),
          finalCash: parseDoubleOrDefault(finalCashController.text),
          finalOvo: parseDoubleOrDefault(finalOvoController.text),
          finalBri: parseDoubleOrDefault(finalBriController.text),
          finalRekpon: parseDoubleOrDefault(finalRekponController.text),
          createdAt: initialIncome?.createdAt,
          updatedAt: DateTime.now(),
          syncStatus: 'pendingUpload',
        );

        final repository = ref.read(incomeRepositoryProvider);
        final result = isEditing
            ? await repository.update(income)
            : await repository.add(income);

        result.when(
          success: (_) {
            // Invalidate providers to refresh data
            ref.invalidate(incomeListProvider);
            ref.invalidate(currentMonthIncomeProvider);
            ref.invalidate(currentMonthTotalProvider);

            if (context.mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(
                    isEditing
                        ? 'Income updated successfully'
                        : 'Income saved successfully',
                  ),
                  backgroundColor: Colors.green,
                ),
              );
              Navigator.of(context).pop();
            }
          },
          failure: (failure) {
            if (context.mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('Error: ${failure.message}'),
                  backgroundColor: Colors.red,
                ),
              );
            }
          },
        );
      } catch (e) {
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Error: $e'), backgroundColor: Colors.red),
          );
        }
      } finally {
        isLoading.value = false;
      }
    }

    return Scaffold(
      appBar: AppBar(
        title: Text(isEditing ? 'Edit Income' : 'Add Income'),
        actions: [
          if (isLoading.value)
            const Center(
              child: Padding(
                padding: EdgeInsets.all(16.0),
                child: SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(strokeWidth: 2),
                ),
              ),
            )
          else
            TextButton(
              onPressed: saveIncome,
              child: Text(isEditing ? 'Update' : 'Save'),
            ),
        ],
      ),
      body: Form(
        key: formKey,
        child: ListView(
          padding: const EdgeInsets.all(16),
          children: [
            // Date Section
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Date',
                      style: Theme.of(context).textTheme.titleMedium,
                    ),
                    const SizedBox(height: 8),
                    TextFormField(
                      controller: dateController,
                      decoration: const InputDecoration(
                        labelText: 'Date',
                        suffixIcon: Icon(Icons.calendar_today),
                        border: OutlineInputBorder(),
                      ),
                      readOnly: true,
                      onTap: selectDate,
                      validator: (value) =>
                          IncomeValidator.validateDate(selectedDate.value),
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // Mileage Section
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      isEditing ? 'Mileage' : 'Initial Mileage',
                      style: Theme.of(context).textTheme.titleMedium,
                    ),
                    const SizedBox(height: 16),
                    if (isEditing)
                      Row(
                        children: [
                          Expanded(
                            child: TextFormField(
                              controller: initialMileageController,
                              decoration: const InputDecoration(
                                labelText: 'Initial Mileage',
                                border: OutlineInputBorder(),
                              ),
                              keyboardType: TextInputType.number,
                              inputFormatters: [
                                FilteringTextInputFormatter.digitsOnly,
                              ],
                              validator: (value) =>
                                  IncomeValidator.validateMileage(
                                    value,
                                    isInitial: true,
                                  ),
                            ),
                          ),
                          const SizedBox(width: 16),
                          Expanded(
                            child: TextFormField(
                              controller: finalMileageController,
                              decoration: const InputDecoration(
                                labelText: 'Final Mileage',
                                border: OutlineInputBorder(),
                              ),
                              keyboardType: TextInputType.number,
                              inputFormatters: [
                                FilteringTextInputFormatter.digitsOnly,
                              ],
                              validator: (value) =>
                                  IncomeValidator.validateMileage(
                                    value,
                                    isInitial: false,
                                  ),
                            ),
                          ),
                        ],
                      )
                    else
                      // Add mode - only show initial mileage
                      TextFormField(
                        controller: initialMileageController,
                        decoration: const InputDecoration(
                          labelText: 'Initial Mileage',
                          border: OutlineInputBorder(),
                        ),
                        keyboardType: TextInputType.number,
                        inputFormatters: [
                          FilteringTextInputFormatter.digitsOnly,
                        ],
                        validator: (value) => IncomeValidator.validateMileage(
                          value,
                          isInitial: true,
                        ),
                      ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // Initial Payment Amounts Section
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Initial Payment Amounts',
                      style: Theme.of(context).textTheme.titleMedium,
                    ),
                    const SizedBox(height: 16),
                    _buildPaymentAmountFields(context, [
                      ('GoPay', initialGopayController),
                      ('BCA', initialBcaController),
                      ('Cash', initialCashController),
                      ('OVO', initialOvoController),
                      ('BRI', initialBriController),
                      ('Rekpon', initialRekponController),
                    ]),
                  ],
                ),
              ),
            ),

            // Final Payment Amounts Section - only show in edit mode
            if (isEditing) ...[
              const SizedBox(height: 16),
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Final Payment Amounts',
                        style: Theme.of(context).textTheme.titleMedium,
                      ),
                      const SizedBox(height: 16),
                      _buildPaymentAmountFields(context, [
                        ('GoPay', finalGopayController),
                        ('BCA', finalBcaController),
                        ('Cash', finalCashController),
                        ('OVO', finalOvoController),
                        ('BRI', finalBriController),
                        ('Rekpon', finalRekponController),
                      ]),
                    ],
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildPaymentAmountFields(
    BuildContext context,
    List<(String, TextEditingController)> fields,
  ) {
    return Column(
      children: [
        for (int i = 0; i < fields.length; i += 2) ...[
          Row(
            children: [
              Expanded(
                child: TextFormField(
                  controller: fields[i].$2,
                  decoration: InputDecoration(
                    labelText: fields[i].$1,
                    border: const OutlineInputBorder(),
                    prefixText: 'Rp ',
                  ),
                  keyboardType: const TextInputType.numberWithOptions(
                    decimal: true,
                  ),
                  inputFormatters: [
                    FilteringTextInputFormatter.allow(RegExp(r'^-?\d*\.?\d*')),
                  ],
                  validator: (value) =>
                      IncomeValidator.validateAmount(value, fields[i].$1),
                ),
              ),
              if (i + 1 < fields.length) ...[
                const SizedBox(width: 16),
                Expanded(
                  child: TextFormField(
                    controller: fields[i + 1].$2,
                    decoration: InputDecoration(
                      labelText: fields[i + 1].$1,
                      border: const OutlineInputBorder(),
                      prefixText: 'Rp ',
                    ),
                    keyboardType: const TextInputType.numberWithOptions(
                      decimal: true,
                    ),
                    inputFormatters: [
                      FilteringTextInputFormatter.allow(
                        RegExp(r'^-?\d*\.?\d*'),
                      ),
                    ],
                    validator: (value) =>
                        IncomeValidator.validateAmount(value, fields[i + 1].$1),
                  ),
                ),
              ] else
                const Expanded(child: SizedBox()),
            ],
          ),
          if (i + 2 < fields.length) const SizedBox(height: 16),
        ],
      ],
    );
  }
}
