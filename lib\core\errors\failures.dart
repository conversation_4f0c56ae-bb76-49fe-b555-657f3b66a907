import 'package:freezed_annotation/freezed_annotation.dart';

part 'failures.freezed.dart';

/// Base failure class for error handling
@freezed
class Failure with _$Failure {
  const factory Failure.server([String? message]) = ServerFailure;
  const factory Failure.cache([String? message]) = CacheFailure;
  const factory Failure.network([String? message]) = NetworkFailure;
  const factory Failure.invalidInput([String? message]) = InvalidInputFailure;
  const factory Failure.database([String? message]) = DatabaseFailure;
  const factory Failure.businessLogic([String? message]) = BusinessLogicFailure;
  const factory Failure.notFound([String? message]) = NotFoundFailure;
  const factory Failure.unexpected([String? message]) = UnexpectedFailure;
}

/// Extension to get user-friendly error messages
extension FailureExtension on Failure {
  String get message {
    return when(
      server: (msg) => msg ?? 'Server error occurred',
      cache: (msg) => msg ?? 'Cache error occurred',
      network: (msg) => msg ?? 'Network error occurred',
      invalidInput: (msg) => msg ?? 'Invalid input provided',
      database: (msg) => msg ?? 'Database error occurred',
      businessLogic: (msg) => msg ?? 'Business logic error occurred',
      notFound: (msg) => msg ?? 'Resource not found',
      unexpected: (msg) => msg ?? 'An unexpected error occurred',
    );
  }
}
