name: drivly
description: "Drivers Performance, Income Tracking and Spare Parts Monitoring App"
publish_to: 'none'
version: 0.1.0

environment:
  sdk: ^3.8.1

dependencies:
  flutter:
    sdk: flutter

  # State Management
  flutter_riverpod: ^2.3.6
  hooks_riverpod: ^2.3.6
  riverpod_annotation: ^2.1.1
  flutter_hooks: ^0.20.0

  # Database & Persistence
  drift: ^2.10.0
  sqlite3_flutter_libs: ^0.5.0
  path_provider: ^2.1.0
  path: ^1.8.3

  # Code Generation
  freezed_annotation: ^2.4.1
  json_annotation: ^4.8.1

  # UI & Utilities
  uuid: ^4.2.2
  intl: ^0.18.1
  fl_chart: ^0.65.0

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^5.0.0

  # Code Generation
  build_runner: ^2.4.6
  riverpod_generator: ^2.2.3
  drift_dev: ^2.10.0
  freezed: ^2.3.5
  json_serializable: ^6.7.0

flutter:
  uses-material-design: true
