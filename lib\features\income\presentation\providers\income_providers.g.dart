// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'income_providers.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$incomeListHash() => r'706908436e4c17b95fa3742e718961bca8eb1ccc';

/// Provider for all income records
///
/// Copied from [incomeList].
@ProviderFor(incomeList)
final incomeListProvider = AutoDisposeFutureProvider<List<Income>>.internal(
  incomeList,
  name: r'incomeListProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$incomeListHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef IncomeListRef = AutoDisposeFutureProviderRef<List<Income>>;
String _$incomeByDateRangeHash() => r'6638eac084d999fc8fcab3157962139a32149d36';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

/// Provider for income records by date range
///
/// Copied from [incomeByDateRange].
@ProviderFor(incomeByDateRange)
const incomeByDateRangeProvider = IncomeByDateRangeFamily();

/// Provider for income records by date range
///
/// Copied from [incomeByDateRange].
class IncomeByDateRangeFamily extends Family<AsyncValue<List<Income>>> {
  /// Provider for income records by date range
  ///
  /// Copied from [incomeByDateRange].
  const IncomeByDateRangeFamily();

  /// Provider for income records by date range
  ///
  /// Copied from [incomeByDateRange].
  IncomeByDateRangeProvider call({
    required DateTime startDate,
    required DateTime endDate,
  }) {
    return IncomeByDateRangeProvider(startDate: startDate, endDate: endDate);
  }

  @override
  IncomeByDateRangeProvider getProviderOverride(
    covariant IncomeByDateRangeProvider provider,
  ) {
    return call(startDate: provider.startDate, endDate: provider.endDate);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'incomeByDateRangeProvider';
}

/// Provider for income records by date range
///
/// Copied from [incomeByDateRange].
class IncomeByDateRangeProvider
    extends AutoDisposeFutureProvider<List<Income>> {
  /// Provider for income records by date range
  ///
  /// Copied from [incomeByDateRange].
  IncomeByDateRangeProvider({
    required DateTime startDate,
    required DateTime endDate,
  }) : this._internal(
         (ref) => incomeByDateRange(
           ref as IncomeByDateRangeRef,
           startDate: startDate,
           endDate: endDate,
         ),
         from: incomeByDateRangeProvider,
         name: r'incomeByDateRangeProvider',
         debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
             ? null
             : _$incomeByDateRangeHash,
         dependencies: IncomeByDateRangeFamily._dependencies,
         allTransitiveDependencies:
             IncomeByDateRangeFamily._allTransitiveDependencies,
         startDate: startDate,
         endDate: endDate,
       );

  IncomeByDateRangeProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.startDate,
    required this.endDate,
  }) : super.internal();

  final DateTime startDate;
  final DateTime endDate;

  @override
  Override overrideWith(
    FutureOr<List<Income>> Function(IncomeByDateRangeRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: IncomeByDateRangeProvider._internal(
        (ref) => create(ref as IncomeByDateRangeRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        startDate: startDate,
        endDate: endDate,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<List<Income>> createElement() {
    return _IncomeByDateRangeProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is IncomeByDateRangeProvider &&
        other.startDate == startDate &&
        other.endDate == endDate;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, startDate.hashCode);
    hash = _SystemHash.combine(hash, endDate.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin IncomeByDateRangeRef on AutoDisposeFutureProviderRef<List<Income>> {
  /// The parameter `startDate` of this provider.
  DateTime get startDate;

  /// The parameter `endDate` of this provider.
  DateTime get endDate;
}

class _IncomeByDateRangeProviderElement
    extends AutoDisposeFutureProviderElement<List<Income>>
    with IncomeByDateRangeRef {
  _IncomeByDateRangeProviderElement(super.provider);

  @override
  DateTime get startDate => (origin as IncomeByDateRangeProvider).startDate;
  @override
  DateTime get endDate => (origin as IncomeByDateRangeProvider).endDate;
}

String _$incomeByMonthHash() => r'f961fd343fd4fe956cf08ce50d0a4f8612de3547';

/// Provider for income records by month
///
/// Copied from [incomeByMonth].
@ProviderFor(incomeByMonth)
const incomeByMonthProvider = IncomeByMonthFamily();

/// Provider for income records by month
///
/// Copied from [incomeByMonth].
class IncomeByMonthFamily extends Family<AsyncValue<List<Income>>> {
  /// Provider for income records by month
  ///
  /// Copied from [incomeByMonth].
  const IncomeByMonthFamily();

  /// Provider for income records by month
  ///
  /// Copied from [incomeByMonth].
  IncomeByMonthProvider call({required int year, required int month}) {
    return IncomeByMonthProvider(year: year, month: month);
  }

  @override
  IncomeByMonthProvider getProviderOverride(
    covariant IncomeByMonthProvider provider,
  ) {
    return call(year: provider.year, month: provider.month);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'incomeByMonthProvider';
}

/// Provider for income records by month
///
/// Copied from [incomeByMonth].
class IncomeByMonthProvider extends AutoDisposeFutureProvider<List<Income>> {
  /// Provider for income records by month
  ///
  /// Copied from [incomeByMonth].
  IncomeByMonthProvider({required int year, required int month})
    : this._internal(
        (ref) =>
            incomeByMonth(ref as IncomeByMonthRef, year: year, month: month),
        from: incomeByMonthProvider,
        name: r'incomeByMonthProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$incomeByMonthHash,
        dependencies: IncomeByMonthFamily._dependencies,
        allTransitiveDependencies:
            IncomeByMonthFamily._allTransitiveDependencies,
        year: year,
        month: month,
      );

  IncomeByMonthProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.year,
    required this.month,
  }) : super.internal();

  final int year;
  final int month;

  @override
  Override overrideWith(
    FutureOr<List<Income>> Function(IncomeByMonthRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: IncomeByMonthProvider._internal(
        (ref) => create(ref as IncomeByMonthRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        year: year,
        month: month,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<List<Income>> createElement() {
    return _IncomeByMonthProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is IncomeByMonthProvider &&
        other.year == year &&
        other.month == month;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, year.hashCode);
    hash = _SystemHash.combine(hash, month.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin IncomeByMonthRef on AutoDisposeFutureProviderRef<List<Income>> {
  /// The parameter `year` of this provider.
  int get year;

  /// The parameter `month` of this provider.
  int get month;
}

class _IncomeByMonthProviderElement
    extends AutoDisposeFutureProviderElement<List<Income>>
    with IncomeByMonthRef {
  _IncomeByMonthProviderElement(super.provider);

  @override
  int get year => (origin as IncomeByMonthProvider).year;
  @override
  int get month => (origin as IncomeByMonthProvider).month;
}

String _$latestIncomeHash() => r'a3d080b9b9ca2c7bf72e7d49dcba3e54f11b9896';

/// Provider for the latest income record
///
/// Copied from [latestIncome].
@ProviderFor(latestIncome)
final latestIncomeProvider = AutoDisposeFutureProvider<Income?>.internal(
  latestIncome,
  name: r'latestIncomeProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$latestIncomeHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef LatestIncomeRef = AutoDisposeFutureProviderRef<Income?>;
String _$totalIncomeForRangeHash() =>
    r'b6307bb244bd42abae478daa5fad1305463ee246';

/// Provider for total income in a date range
///
/// Copied from [totalIncomeForRange].
@ProviderFor(totalIncomeForRange)
const totalIncomeForRangeProvider = TotalIncomeForRangeFamily();

/// Provider for total income in a date range
///
/// Copied from [totalIncomeForRange].
class TotalIncomeForRangeFamily extends Family<AsyncValue<double>> {
  /// Provider for total income in a date range
  ///
  /// Copied from [totalIncomeForRange].
  const TotalIncomeForRangeFamily();

  /// Provider for total income in a date range
  ///
  /// Copied from [totalIncomeForRange].
  TotalIncomeForRangeProvider call({
    required DateTime startDate,
    required DateTime endDate,
  }) {
    return TotalIncomeForRangeProvider(startDate: startDate, endDate: endDate);
  }

  @override
  TotalIncomeForRangeProvider getProviderOverride(
    covariant TotalIncomeForRangeProvider provider,
  ) {
    return call(startDate: provider.startDate, endDate: provider.endDate);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'totalIncomeForRangeProvider';
}

/// Provider for total income in a date range
///
/// Copied from [totalIncomeForRange].
class TotalIncomeForRangeProvider extends AutoDisposeFutureProvider<double> {
  /// Provider for total income in a date range
  ///
  /// Copied from [totalIncomeForRange].
  TotalIncomeForRangeProvider({
    required DateTime startDate,
    required DateTime endDate,
  }) : this._internal(
         (ref) => totalIncomeForRange(
           ref as TotalIncomeForRangeRef,
           startDate: startDate,
           endDate: endDate,
         ),
         from: totalIncomeForRangeProvider,
         name: r'totalIncomeForRangeProvider',
         debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
             ? null
             : _$totalIncomeForRangeHash,
         dependencies: TotalIncomeForRangeFamily._dependencies,
         allTransitiveDependencies:
             TotalIncomeForRangeFamily._allTransitiveDependencies,
         startDate: startDate,
         endDate: endDate,
       );

  TotalIncomeForRangeProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.startDate,
    required this.endDate,
  }) : super.internal();

  final DateTime startDate;
  final DateTime endDate;

  @override
  Override overrideWith(
    FutureOr<double> Function(TotalIncomeForRangeRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: TotalIncomeForRangeProvider._internal(
        (ref) => create(ref as TotalIncomeForRangeRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        startDate: startDate,
        endDate: endDate,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<double> createElement() {
    return _TotalIncomeForRangeProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is TotalIncomeForRangeProvider &&
        other.startDate == startDate &&
        other.endDate == endDate;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, startDate.hashCode);
    hash = _SystemHash.combine(hash, endDate.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin TotalIncomeForRangeRef on AutoDisposeFutureProviderRef<double> {
  /// The parameter `startDate` of this provider.
  DateTime get startDate;

  /// The parameter `endDate` of this provider.
  DateTime get endDate;
}

class _TotalIncomeForRangeProviderElement
    extends AutoDisposeFutureProviderElement<double>
    with TotalIncomeForRangeRef {
  _TotalIncomeForRangeProviderElement(super.provider);

  @override
  DateTime get startDate => (origin as TotalIncomeForRangeProvider).startDate;
  @override
  DateTime get endDate => (origin as TotalIncomeForRangeProvider).endDate;
}

String _$averageDailyIncomeHash() =>
    r'63f41169f954ec7b5c4cbea343a7e225df9da60f';

/// Provider for average daily income in a date range
///
/// Copied from [averageDailyIncome].
@ProviderFor(averageDailyIncome)
const averageDailyIncomeProvider = AverageDailyIncomeFamily();

/// Provider for average daily income in a date range
///
/// Copied from [averageDailyIncome].
class AverageDailyIncomeFamily extends Family<AsyncValue<double>> {
  /// Provider for average daily income in a date range
  ///
  /// Copied from [averageDailyIncome].
  const AverageDailyIncomeFamily();

  /// Provider for average daily income in a date range
  ///
  /// Copied from [averageDailyIncome].
  AverageDailyIncomeProvider call({
    required DateTime startDate,
    required DateTime endDate,
  }) {
    return AverageDailyIncomeProvider(startDate: startDate, endDate: endDate);
  }

  @override
  AverageDailyIncomeProvider getProviderOverride(
    covariant AverageDailyIncomeProvider provider,
  ) {
    return call(startDate: provider.startDate, endDate: provider.endDate);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'averageDailyIncomeProvider';
}

/// Provider for average daily income in a date range
///
/// Copied from [averageDailyIncome].
class AverageDailyIncomeProvider extends AutoDisposeFutureProvider<double> {
  /// Provider for average daily income in a date range
  ///
  /// Copied from [averageDailyIncome].
  AverageDailyIncomeProvider({
    required DateTime startDate,
    required DateTime endDate,
  }) : this._internal(
         (ref) => averageDailyIncome(
           ref as AverageDailyIncomeRef,
           startDate: startDate,
           endDate: endDate,
         ),
         from: averageDailyIncomeProvider,
         name: r'averageDailyIncomeProvider',
         debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
             ? null
             : _$averageDailyIncomeHash,
         dependencies: AverageDailyIncomeFamily._dependencies,
         allTransitiveDependencies:
             AverageDailyIncomeFamily._allTransitiveDependencies,
         startDate: startDate,
         endDate: endDate,
       );

  AverageDailyIncomeProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.startDate,
    required this.endDate,
  }) : super.internal();

  final DateTime startDate;
  final DateTime endDate;

  @override
  Override overrideWith(
    FutureOr<double> Function(AverageDailyIncomeRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: AverageDailyIncomeProvider._internal(
        (ref) => create(ref as AverageDailyIncomeRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        startDate: startDate,
        endDate: endDate,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<double> createElement() {
    return _AverageDailyIncomeProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is AverageDailyIncomeProvider &&
        other.startDate == startDate &&
        other.endDate == endDate;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, startDate.hashCode);
    hash = _SystemHash.combine(hash, endDate.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin AverageDailyIncomeRef on AutoDisposeFutureProviderRef<double> {
  /// The parameter `startDate` of this provider.
  DateTime get startDate;

  /// The parameter `endDate` of this provider.
  DateTime get endDate;
}

class _AverageDailyIncomeProviderElement
    extends AutoDisposeFutureProviderElement<double>
    with AverageDailyIncomeRef {
  _AverageDailyIncomeProviderElement(super.provider);

  @override
  DateTime get startDate => (origin as AverageDailyIncomeProvider).startDate;
  @override
  DateTime get endDate => (origin as AverageDailyIncomeProvider).endDate;
}

String _$currentMonthIncomeHash() =>
    r'017cf6d2fd3fb16e9689a3496b38cc7dd6cc5aa4';

/// Provider for current month's income
///
/// Copied from [currentMonthIncome].
@ProviderFor(currentMonthIncome)
final currentMonthIncomeProvider =
    AutoDisposeFutureProvider<List<Income>>.internal(
      currentMonthIncome,
      name: r'currentMonthIncomeProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$currentMonthIncomeHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef CurrentMonthIncomeRef = AutoDisposeFutureProviderRef<List<Income>>;
String _$currentMonthTotalHash() => r'1ddc1cbd8acd4eb2453404cab8791ee9ff84e7f3';

/// Provider for current month's total income
///
/// Copied from [currentMonthTotal].
@ProviderFor(currentMonthTotal)
final currentMonthTotalProvider = AutoDisposeFutureProvider<double>.internal(
  currentMonthTotal,
  name: r'currentMonthTotalProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$currentMonthTotalHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef CurrentMonthTotalRef = AutoDisposeFutureProviderRef<double>;
String _$currentYearIncomeHash() => r'0e0f1036521497b2c4f930c786e4e7faefce780b';

/// Provider for current year's income
///
/// Copied from [currentYearIncome].
@ProviderFor(currentYearIncome)
final currentYearIncomeProvider =
    AutoDisposeFutureProvider<List<Income>>.internal(
      currentYearIncome,
      name: r'currentYearIncomeProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$currentYearIncomeHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef CurrentYearIncomeRef = AutoDisposeFutureProviderRef<List<Income>>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
