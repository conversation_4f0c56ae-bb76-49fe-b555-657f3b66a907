import 'package:drift/drift.dart';
import 'package:flutter/foundation.dart';
import '../../../../core/datasources/database.dart';
import '../../../../core/errors/failures.dart';
import '../../../../core/utils/result.dart';
import '../../domain/entities/income.dart';
import '../../domain/repositories/income_repository.dart';
import '../models/income_model.dart';

/// Implementation of IncomeRepository using Drift ORM
class IncomeRepositoryImpl implements IncomeRepository {
  final AppDatabase _database;

  IncomeRepositoryImpl(this._database);

  @override
  Future<Result<int>> add(Income entity) async {
    try {
      final id = await _database
          .into(_database.incomeTable)
          .insert(entity.toCompanion());
      return Result.success(id);
    } catch (e) {
      return Result.failure(Failure.database(e.toString()));
    }
  }

  @override
  Future<Result<bool>> update(Income entity) async {
    try {
      if (entity.uuid == null) {
        return const Result.failure(
          Failure.invalidInput('UUID is required for update'),
        );
      }

      final rowsAffected =
          await (_database.update(_database.incomeTable)
                ..where((tbl) => tbl.uuid.equals(entity.uuid!)))
              .write(entity.toUpdateCompanion());

      return Result.success(rowsAffected > 0);
    } catch (e) {
      return Result.failure(Failure.database(e.toString()));
    }
  }

  @override
  Future<Result<bool>> delete(int id) async {
    try {
      // Soft delete by setting deletedAt timestamp
      final rowsAffected =
          await (_database.update(
            _database.incomeTable,
          )..where((tbl) => tbl.id.equals(id))).write(
            IncomeTableCompanion(
              deletedAt: Value(DateTime.now()),
              updatedAt: Value(DateTime.now()),
              syncStatus: const Value('pendingUpload'),
            ),
          );

      return Result.success(rowsAffected > 0);
    } catch (e) {
      return Result.failure(Failure.database(e.toString()));
    }
  }

  @override
  Future<Result<Income?>> getById(int id) async {
    try {
      final query = _database.select(_database.incomeTable)
        ..where((tbl) => tbl.id.equals(id) & tbl.deletedAt.isNull());

      final result = await query.getSingleOrNull();
      return Result.success(result?.toEntity());
    } catch (e) {
      return Result.failure(Failure.database(e.toString()));
    }
  }

  @override
  Future<Result<List<Income>>> getAll() async {
    try {
      final query = _database.select(_database.incomeTable)
        ..where((tbl) => tbl.deletedAt.isNull())
        ..orderBy([(tbl) => OrderingTerm.desc(tbl.date)]);

      final results = await query.get();
      debugPrint('DEBUG: Repository getAll() found ${results.length} records');

      final incomes = results.map((data) => data.toEntity()).toList();
      debugPrint('DEBUG: Converted to ${incomes.length} Income entities');

      return Result.success(incomes);
    } catch (e) {
      debugPrint('DEBUG: Repository getAll() error: $e');
      return Result.failure(Failure.database(e.toString()));
    }
  }

  @override
  Future<Result<List<Income>>> getPaginated({
    int offset = 0,
    int limit = 20,
  }) async {
    try {
      final query = _database.select(_database.incomeTable)
        ..where((tbl) => tbl.deletedAt.isNull())
        ..orderBy([(tbl) => OrderingTerm.desc(tbl.date)])
        ..limit(limit, offset: offset);

      final results = await query.get();
      final incomes = results.map((data) => data.toEntity()).toList();
      return Result.success(incomes);
    } catch (e) {
      return Result.failure(Failure.database(e.toString()));
    }
  }

  @override
  Future<Result<List<Income>>> getByDateRange({
    required DateTime startDate,
    required DateTime endDate,
  }) async {
    try {
      final query = _database.select(_database.incomeTable)
        ..where(
          (tbl) =>
              tbl.deletedAt.isNull() &
              tbl.date.isBetweenValues(startDate, endDate),
        )
        ..orderBy([(tbl) => OrderingTerm.desc(tbl.date)]);

      final results = await query.get();
      final incomes = results.map((data) => data.toEntity()).toList();
      return Result.success(incomes);
    } catch (e) {
      return Result.failure(Failure.database(e.toString()));
    }
  }

  @override
  Future<Result<List<Income>>> getByMonth({
    required int year,
    required int month,
  }) async {
    try {
      final startDate = DateTime(year, month, 1);
      final endDate = DateTime(
        year,
        month + 1,
        1,
      ).subtract(const Duration(days: 1));

      return await getByDateRange(startDate: startDate, endDate: endDate);
    } catch (e) {
      return Result.failure(Failure.database(e.toString()));
    }
  }

  @override
  Future<Result<List<Income>>> getByYear(int year) async {
    try {
      final startDate = DateTime(year, 1, 1);
      final endDate = DateTime(year, 12, 31);

      return await getByDateRange(startDate: startDate, endDate: endDate);
    } catch (e) {
      return Result.failure(Failure.database(e.toString()));
    }
  }

  @override
  Future<Result<Income?>> getLatest() async {
    try {
      final query = _database.select(_database.incomeTable)
        ..where((tbl) => tbl.deletedAt.isNull())
        ..orderBy([(tbl) => OrderingTerm.desc(tbl.date)])
        ..limit(1);

      final result = await query.getSingleOrNull();
      return Result.success(result?.toEntity());
    } catch (e) {
      return Result.failure(Failure.database(e.toString()));
    }
  }

  @override
  Future<Result<double>> getTotalIncomeForRange({
    required DateTime startDate,
    required DateTime endDate,
  }) async {
    try {
      // We need to calculate the sum of net income for each record
      // Since net income is computed, we'll get all records and calculate
      final incomeResult = await getByDateRange(
        startDate: startDate,
        endDate: endDate,
      );

      return incomeResult.when(
        success: (incomes) => Result.success(
          incomes.fold(0.0, (sum, income) => sum + income.netIncome),
        ),
        failure: (failure) => Result.failure(failure),
      );
    } catch (e) {
      return Result.failure(Failure.database(e.toString()));
    }
  }

  @override
  Future<Result<double>> getAverageDailyIncome({
    required DateTime startDate,
    required DateTime endDate,
  }) async {
    try {
      final totalResult = await getTotalIncomeForRange(
        startDate: startDate,
        endDate: endDate,
      );

      return totalResult.when(
        success: (total) {
          final days = endDate.difference(startDate).inDays + 1;
          return Result.success(days > 0 ? total / days : 0.0);
        },
        failure: (failure) => Result.failure(failure),
      );
    } catch (e) {
      return Result.failure(Failure.database(e.toString()));
    }
  }

  @override
  Future<Result<List<Income>>> search({
    DateTime? date,
    double? minAmount,
    double? maxAmount,
  }) async {
    try {
      // For amount-based search, we need to get all records and filter by computed values
      // This is not optimal but necessary since net income is computed
      final allResult = await getAll();

      return allResult.when(
        success: (incomes) {
          final filtered = incomes.where((income) {
            bool matches = true;

            if (date != null) {
              matches =
                  matches &&
                  income.date.year == date.year &&
                  income.date.month == date.month &&
                  income.date.day == date.day;
            }

            if (minAmount != null) {
              matches = matches && income.netIncome >= minAmount;
            }

            if (maxAmount != null) {
              matches = matches && income.netIncome <= maxAmount;
            }

            return matches;
          }).toList();

          return Result.success(filtered);
        },
        failure: (failure) => Result.failure(failure),
      );
    } catch (e) {
      return Result.failure(Failure.database(e.toString()));
    }
  }

  @override
  Future<Result<void>> syncData() async {
    // TODO: Implement cloud synchronization
    return const Result.success(null);
  }

  @override
  Future<Result<List<Income>>> getPendingSync() async {
    try {
      final query = _database.select(_database.incomeTable)
        ..where(
          (tbl) =>
              tbl.deletedAt.isNull() & tbl.syncStatus.equals('pendingUpload'),
        )
        ..orderBy([(tbl) => OrderingTerm.desc(tbl.updatedAt)]);

      final results = await query.get();
      final incomes = results.map((data) => data.toEntity()).toList();
      return Result.success(incomes);
    } catch (e) {
      return Result.failure(Failure.database(e.toString()));
    }
  }

  @override
  Future<Result<bool>> markAsSynced(int id) async {
    try {
      final rowsAffected =
          await (_database.update(
            _database.incomeTable,
          )..where((tbl) => tbl.id.equals(id))).write(
            const IncomeTableCompanion(
              syncStatus: Value('synced'),
              updatedAt: Value.absent(),
            ),
          );

      return Result.success(rowsAffected > 0);
    } catch (e) {
      return Result.failure(Failure.database(e.toString()));
    }
  }
}
