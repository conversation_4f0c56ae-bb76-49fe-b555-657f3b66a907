import 'package:riverpod_annotation/riverpod_annotation.dart';
import '../datasources/database.dart';

part 'database_provider.g.dart';

/// Provides the main application database instance
@Riverpod(keepAlive: true)
AppDatabase database(DatabaseRef ref) {
  final database = AppDatabase();
  
  // Ensure database is properly disposed when the app closes
  ref.onDispose(() {
    database.close();
  });
  
  return database;
}
