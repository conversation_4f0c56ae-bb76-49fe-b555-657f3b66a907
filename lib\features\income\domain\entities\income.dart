import 'package:freezed_annotation/freezed_annotation.dart';

part 'income.freezed.dart';
part 'income.g.dart';

/// Income entity representing daily income tracking
@freezed
class Income with _$Income {
  const factory Income({
    String? uuid,
    int? id,
    required DateTime date,
    required int initialMileage,
    required int finalMileage,
    required double initialGopay,
    required double initialBca,
    required double initialCash,
    required double initialOvo,
    required double initialBri,
    required double initialRekpon,
    required double finalGopay,
    required double finalBca,
    required double finalCash,
    required double finalOvo,
    required double finalBri,
    required double finalRekpon,
    DateTime? createdAt,
    DateTime? updatedAt,
    DateTime? deletedAt,
    @Default('pendingUpload') String syncStatus,
  }) = _Income;

  factory Income.fromJson(Map<String, dynamic> json) => _$IncomeFromJson(json);
}

/// Extension for computed fields
extension IncomeComputed on Income {
  /// Computed mileage (final - initial)
  int get mileage => finalMileage - initialMileage;

  /// Computed initial capital (sum of all initial amounts)
  double get initialCapital =>
      initialGopay + initialBca + initialCash + initialOvo + initialBri + initialRekpon;

  /// Computed final result (sum of all final amounts)
  double get finalResult =>
      finalGopay + finalBca + finalCash + finalOvo + finalBri + finalRekpon;

  /// Computed net income (final - initial)
  double get netIncome => finalResult - initialCapital;

  /// Check if income record is valid
  bool get isValid {
    return finalMileage >= initialMileage &&
           initialGopay >= 0 &&
           initialBca >= 0 &&
           initialCash >= 0 &&
           initialOvo >= 0 &&
           initialBri >= 0 &&
           initialRekpon >= 0 &&
           finalGopay >= 0 &&
           finalBca >= 0 &&
           finalCash >= 0 &&
           finalOvo >= 0 &&
           finalBri >= 0 &&
           finalRekpon >= 0;
  }
}
