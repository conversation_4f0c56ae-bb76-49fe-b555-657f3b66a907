import '../../../../core/repositories/base_repository.dart';
import '../../../../core/utils/result.dart';
import '../entities/income.dart';

/// Repository interface for income operations
abstract class IncomeRepository extends SyncableRepository<Income, int> {
  /// Get income records for a specific date range
  Future<Result<List<Income>>> getByDateRange({
    required DateTime startDate,
    required DateTime endDate,
  });

  /// Get income records for a specific month
  Future<Result<List<Income>>> getByMonth({
    required int year,
    required int month,
  });

  /// Get income records for a specific year
  Future<Result<List<Income>>> getByYear(int year);

  /// Get the latest income record
  Future<Result<Income?>> getLatest();

  /// Get total income for a date range
  Future<Result<double>> getTotalIncomeForRange({
    required DateTime startDate,
    required DateTime endDate,
  });

  /// Get average daily income for a date range
  Future<Result<double>> getAverageDailyIncome({
    required DateTime startDate,
    required DateTime endDate,
  });

  /// Search income records by date or amount
  Future<Result<List<Income>>> search({
    DateTime? date,
    double? minAmount,
    double? maxAmount,
  });
}
