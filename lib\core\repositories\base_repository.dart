import '../utils/result.dart';

/// Base repository interface for CRUD operations
abstract class BaseRepository<T, ID> {
  /// Add a new entity
  Future<Result<ID>> add(T entity);

  /// Update an existing entity
  Future<Result<bool>> update(T entity);

  /// Delete an entity by ID
  Future<Result<bool>> delete(ID id);

  /// Get an entity by ID
  Future<Result<T?>> getById(ID id);

  /// Get all entities
  Future<Result<List<T>>> getAll();

  /// Get entities with pagination
  Future<Result<List<T>>> getPaginated({
    int offset = 0,
    int limit = 20,
  });
}

/// Syncable repository interface for entities that support cloud sync
abstract class SyncableRepository<T, ID> extends BaseRepository<T, ID> {
  /// Sync data with remote server
  Future<Result<void>> syncData();

  /// Get entities that need to be synced
  Future<Result<List<T>>> getPendingSync();

  /// Mark entity as synced
  Future<Result<bool>> markAsSynced(ID id);
}
